import { Injectable, OnModuleDestroy } from '@nestjs/common';
import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';

export interface HeaderFooterOptions {
  companyName?: string;
  companyPhone?: string;
  companyWebsite?: string;
  documentTitle?: string;
  showDate?: boolean;
  showPageNumbers?: boolean;
  customHeaderContent?: string;
  customFooterContent?: string;
  backgroundImage?: string; // URL or base64 data URL for background image
  backgroundImageOpacity?: number; // Opacity for background image (0-1)
}

@Injectable()
export class PuppeteerService implements OnModuleDestroy {
  private browser: Browser | null = null;

  /**
   * Generate custom header template with dynamic content
   */
  private generateHeaderTemplate(options: HeaderFooterOptions = {}): string {
    const {
      companyName = 'Lumigo Transport',
      documentTitle = '',
      showDate = true,
      customHeaderContent,
    } = options;

    if (customHeaderContent) {
      return customHeaderContent;
    }

    return `
      <div style="font-size: 10px; padding: 5px 20px; width: 100%; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e3e5f6; background: white;">
        <div style="display: flex; align-items: center;">
          <span style="font-weight: 600; color: #090a1a;">${companyName}</span>
          ${documentTitle ? `<span style="margin-left: 10px; color: #667085;">- ${documentTitle}</span>` : ''}
        </div>
        ${showDate ? '<div style="color: #667085;"><span class="date"></span></div>' : ''}
      </div>
    `;
  }

  /**
   * Generate custom footer template with dynamic content
   */
  private generateFooterTemplate(options: HeaderFooterOptions = {}): string {
    const {
      companyPhone = '************',
      companyWebsite = 'www.lumigotransport.ca',
      showPageNumbers = true,
      customFooterContent,
    } = options;

    if (customFooterContent) {
      return customFooterContent;
    }

    return `
      <div style="font-size: 9px; padding: 5px 20px; width: 100%; display: flex; justify-content: space-between; align-items: center; border-top: 1px solid #e3e5f6; background: white; color: #667085;">
        <div>Phone: ${companyPhone} | ${companyWebsite}</div>
        ${showPageNumbers ? '<div>Page <span class="pageNumber"></span> of <span class="totalPages"></span></div>' : ''}
      </div>
    `;
  }

  /**
   * Inject background image CSS into HTML content for all pages
   */
  private injectBackgroundImageCSS(
    htmlContent: string,
    options: HeaderFooterOptions = {},
  ): string {
    const { backgroundImage, backgroundImageOpacity = 0.1 } = options;

    if (!backgroundImage) {
      return htmlContent;
    }

    const backgroundCSS = `
      <style>
        @page {
          background-image: url('${backgroundImage}');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
        }

        /* Alternative approach using pseudo-element for better browser support */
        body::before {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image: url('${backgroundImage}');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          opacity: ${backgroundImageOpacity};
          z-index: -1;
          pointer-events: none;
        }
      </style>
    `;

    // Insert the CSS before the closing </head> tag
    return htmlContent.replace('</head>', `${backgroundCSS}</head>`);
  }

  async initBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });
    }
    return this.browser;
  }

  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async generatePDFFromHTML(
    htmlContent: string,
    options?: any,
    headerFooterOptions?: HeaderFooterOptions,
  ): Promise<Buffer> {
    const browser = await this.initBrowser();
    const page = await browser.newPage();
    try {
      // Set viewport for consistent rendering
      await page.setViewport({ width: 1200, height: 800 });

      // Inject background image CSS if provided
      const processedHtml = this.injectBackgroundImageCSS(
        htmlContent,
        headerFooterOptions,
      );

      // Use 'domcontentloaded' for faster rendering since we have inline CSS
      await page.setContent(processedHtml, {
        waitUntil: 'domcontentloaded',
        timeout: 10000, // Reduced timeout
      });

      await page.evaluateHandle('document.fonts.ready');
      await page.evaluate(() => {
        return Promise.all(
          Array.from(document.images).map((img) => {
            if (img.complete) return Promise.resolve();
            return new Promise((resolve) => {
              img.onload = resolve;
              img.onerror = resolve;
            });
          }),
        );
      });

      // Wait a bit for fonts to render properly
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Generate header and footer templates
      const headerTemplate = this.generateHeaderTemplate(headerFooterOptions);
      const footerTemplate = this.generateFooterTemplate(headerFooterOptions);

      // Merge default options with provided options
      const pdfOptions = {
        format: 'A4',
        printBackground: true,
        margin: {
          top: '80px', // Increased to accommodate header
          right: '20px',
          bottom: '60px', // Increased to accommodate footer
          left: '20px',
        },
        displayHeaderFooter: true,

        headerTemplate: headerTemplate,
        footerTemplate: footerTemplate,
        ...options, // Allow overriding defaults
      };

      const pdfBuffer = await page.pdf(pdfOptions);
      return Buffer.from(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  async onModuleDestroy() {
    await this.closeBrowser();
  }
}
